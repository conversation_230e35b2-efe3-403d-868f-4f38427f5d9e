package functions

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	"github.com/go-logr/logr"
	"github.com/sasha<PERSON>nov/go-openai/jsonschema"

	"github.com/akuityio/akuity-platform/internal/services/ai/clients"
	"github.com/akuityio/akuity-platform/internal/services/ai/reposet"
	"github.com/akuityio/akuity-platform/internal/services/ai/util"
	"github.com/akuityio/akuity-platform/internal/services/k8sresource"
	"github.com/akuityio/akuity-platform/internal/utils/ai"
	"github.com/akuityio/akuity-platform/models/models"
	organizationv1 "github.com/akuityio/akuity-platform/pkg/api/gen/organization/v1"
	featuresv1 "github.com/akuityio/akuity-platform/pkg/api/gen/types/features/v1"

	_ "embed"
)

type (
	conversationCtx     struct{}
	conversationMetaCtx struct{}
)

var (
	//go:embed prompt.md
	Prompt string

	conversationKey     = &conversationCtx{}
	conversationMetaKey = &conversationMetaCtx{}
)

type EmptyArgs struct {
	Dummy string `json:"dummy" description:"dummy parameter, any value works"`
}

func WithConversation(ctx context.Context, conversation *models.AiConversation) context.Context {
	return context.WithValue(ctx, conversationKey, conversation)
}

func GetConversation(ctx context.Context) *models.AiConversation {
	if conv, ok := ctx.Value(conversationKey).(*models.AiConversation); ok {
		return conv
	}
	return nil
}

func WithConversationMeta(ctx context.Context, meta *models.AIConversationMetadata) context.Context {
	return context.WithValue(ctx, conversationMetaKey, meta)
}

func GetConversationMeta(ctx context.Context) *models.AIConversationMetadata {
	if meta, ok := ctx.Value(conversationMetaKey).(*models.AIConversationMetadata); ok {
		return meta
	}
	return nil
}

type ResourceID struct {
	Group     string `json:"group"`
	Version   string `json:"version"`
	Kind      string `json:"kind"`
	Name      string `json:"name"`
	Namespace string `json:"namespace"`
}

type Function struct {
	ai.Tool

	DisplayName           string // Tool display name, used in the UI
	CheckContextSupported func(ctx context.Context, context models.ConversationContext) bool
	PromptSuggestion      *organizationv1.AIConversationSuggestion
	Callback              func(ctx context.Context, args []byte) (string, error)
	Internal              bool // Indicates if the function is internal and should not be exposed to the user
}

type Controller struct {
	OrganizationID  string
	ArgoCDClientSet *clients.ArgoCDClientSet
	KargoClientSet  *clients.KargoClientSet
	K8sClientSet    *clients.K8sClientSet
	RepoSet         reposet.ResourceRepoSet
	Log             logr.Logger
	ResSvc          *k8sresource.Service

	aiClient  *clients.AIClientSet
	functions []Function
}

func NewController(ctx context.Context, organizationID string, resourceRepoSet reposet.ResourceRepoSet, resSvc *k8sresource.Service, log logr.Logger, aiClient *clients.AIClientSet, k8sClientSet *clients.K8sClientSet, featureStatuses *featuresv1.FeatureStatuses) (*Controller, error) {
	argoCDClientSet, err := clients.NewArgoCDClientSet(ctx, resourceRepoSet)
	if err != nil {
		return nil, err
	}

	var kargoClientSet *clients.KargoClientSet
	if featureStatuses.GetKargoEnterprise().Enabled() {
		kargoClientSet, err = clients.NewKargoClientSet(ctx, resourceRepoSet, k8sClientSet)
		if err != nil {
			return nil, err
		}
	}

	ctrl := &Controller{
		OrganizationID:  organizationID,
		ArgoCDClientSet: argoCDClientSet,
		KargoClientSet:  kargoClientSet,
		K8sClientSet:    k8sClientSet,
		RepoSet:         resourceRepoSet,
		Log:             log,
		ResSvc:          resSvc,
		aiClient:        aiClient,
	}
	if err := ctrl.addK8SNamespaceFunctions(); err != nil {
		return nil, err
	}
	if err := ctrl.addSharedFunctions(); err != nil {
		return nil, err
	}
	if err := ctrl.addArgoCDAppFunctions(); err != nil {
		return nil, err
	}
	if kargoClientSet != nil {
		if err := ctrl.addKargoFunctions(); err != nil {
			return nil, err
		}
	}
	if err := ctrl.addScheduleFunctions(); err != nil {
		return nil, err
	}
	if err := ctrl.addSearchFunctions(); err != nil {
		return nil, err
	}
	if err := ctrl.addIncidentFunctions(); err != nil {
		return nil, err
	}
	if err := ctrl.addPromotionAdvisorFunctions(); err != nil {
		return nil, err
	}
	if err := ctrl.addNotificationsFunctions(); err != nil {
		return nil, err
	}

	return ctrl, nil
}

func (fc *Controller) GetFunctions() []Function {
	return fc.functions
}

func addFunction[T any](fc *Controller, f Function, callback func(ctx context.Context, in T) (string, error)) error {
	funcType := reflect.TypeOf(callback)
	paramType := funcType.In(1)
	generated, err := jsonschema.GenerateSchemaForType(reflect.New(paramType).Elem().Interface())
	if err != nil {
		return err
	}
	data, err := json.Marshal(generated)
	if err != nil {
		return err
	}
	var definition jsonschema.Definition
	if err := json.Unmarshal(data, &definition); err != nil {
		return err
	}

	f.Callback = func(ctx context.Context, args []byte) (string, error) {
		var val T
		if len(args) != 0 {
			if err := json.Unmarshal(args, &val); err != nil {
				return "", util.NewInvalidArgsError(fmt.Sprintf("invalid JSON args: %v", err))
			}
		}
		return callback(ctx, val)
	}
	f.ParamsSchema = definition
	fc.functions = append(fc.functions, f)
	return nil
}
